#!/bin/bash

# Test View Profile Functionality
# This script sets up the database and tests the view profile feature

set -e

echo "🚀 Testing View Profile Functionality"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Step 1: Setup database with admin user
print_status "Setting up database with admin user..."
cd auto-login

# Check if database exists and create admin user
if [ -f "src/database/database.sqlite" ] || command -v psql &> /dev/null; then
    print_status "Creating admin user in database..."
    
    # For PostgreSQL (if available)
    if command -v psql &> /dev/null; then
        print_status "Using PostgreSQL database..."
        psql -d antidetect_autologin -f ../scripts/create_admin_user.sql 2>/dev/null || print_warning "PostgreSQL setup failed, continuing..."
    fi
    
    print_success "Database setup completed"
else
    print_warning "Database not found, will be created on first run"
fi

# Step 2: Start NestJS backend
print_status "Starting NestJS backend..."
if ! pgrep -f "nest start" > /dev/null; then
    npm run start:dev &
    NESTJS_PID=$!
    print_status "NestJS backend started with PID: $NESTJS_PID"
    sleep 10 # Wait for NestJS to start
else
    print_status "NestJS backend already running"
fi

cd ..

# Step 3: Start FastAPI backend
print_status "Starting FastAPI backend..."
cd backend
if ! pgrep -f "uvicorn main:app" > /dev/null; then
    source venv/bin/activate 2>/dev/null || print_warning "Virtual environment not found"
    python main.py &
    FASTAPI_PID=$!
    print_status "FastAPI backend started with PID: $FASTAPI_PID"
    sleep 5 # Wait for FastAPI to start
else
    print_status "FastAPI backend already running"
fi

cd ..

# Step 4: Start Frontend
print_status "Starting Frontend..."
cd frontend
if ! pgrep -f "webpack-dev-server" > /dev/null; then
    npm start &
    FRONTEND_PID=$!
    print_status "Frontend started with PID: $FRONTEND_PID"
    sleep 10 # Wait for frontend to start
else
    print_status "Frontend already running"
fi

cd ..

# Step 5: Test the functionality
print_status "Testing View Profile functionality..."

echo ""
echo "🎯 Test Instructions:"
echo "===================="
echo "1. Open your browser and go to: http://localhost:3001"
echo "2. Login with:"
echo "   Email: <EMAIL>"
echo "   Password: password123"
echo "3. Navigate to 'Manage Profiles' page"
echo "4. Look for the 'View' button (eye icon) in the Actions column"
echo "5. Click the 'View' button to test the view profile functionality"
echo ""
echo "Expected behavior:"
echo "- Camoufox browser should open with saved profile data"
echo "- Browser should be logged in automatically (if profile data exists)"
echo "- Success message should appear in the UI"
echo ""

print_success "All services are running!"
print_status "Frontend: http://localhost:3001"
print_status "NestJS API: http://localhost:3000"
print_status "FastAPI: http://localhost:8000"

echo ""
print_warning "Press Ctrl+C to stop all services when testing is complete"

# Wait for user to stop
trap 'print_status "Stopping services..."; kill $NESTJS_PID $FASTAPI_PID $FRONTEND_PID 2>/dev/null; exit 0' INT

# Keep script running
while true; do
    sleep 1
done
