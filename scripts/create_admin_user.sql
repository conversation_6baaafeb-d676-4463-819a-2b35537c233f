-- Create admin user for testing view profile functionality
-- Email: <EMAIL>
-- Password: password123
-- Run this script against the NestJS database

-- Insert admin user
INSERT INTO users (email, password, plain_password, status, role, created_at, updated_at) 
VALUES (
  '<EMAIL>', 
  '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- bcrypt hash for 'password123'
  'password123',
  'active', 
  'admin', 
  NOW(), 
  NOW()
)
ON CONFLICT (email) DO UPDATE SET
  password = EXCLUDED.password,
  plain_password = EXCLUDED.plain_password,
  status = EXCLUDED.status,
  role = EXCLUDED.role,
  updated_at = NOW();

-- Create some test accounts for the admin user to manage
INSERT INTO accounts (website_url, username, password, name, description, created_at, updated_at) 
VALUES 
  ('https://facebook.com', 'test_user_1', 'test_pass_1', 'Test Facebook Account 1', 'Test account for profile management', NOW(), NOW()),
  ('https://facebook.com', 'test_user_2', 'test_pass_2', 'Test Facebook Account 2', 'Another test account for profile management', NOW(), NOW()),
  ('https://facebook.com', 'test_user_3', 'test_pass_3', 'Test Facebook Account 3', 'Third test account for profile management', NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Display created users and accounts
SELECT 'Created Users:' as info;
SELECT id, email, role, status, created_at FROM users WHERE email = '<EMAIL>';

SELECT 'Created Accounts:' as info;
SELECT id, name, website_url, username, created_at FROM accounts ORDER BY id DESC LIMIT 3;
