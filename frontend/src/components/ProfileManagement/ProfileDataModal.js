/**
 * ProfileDataModal Component - Display and manage profile data
 * Refactored to use shared browser data capture utilities
 */

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Tabs, 
  Card, 
  Typography, 
  Space, 
  Button, 
  message, 
  Spin,
  Empty,
  Tag,
  Descriptions
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  HistoryOutlined,
  SettingOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import { apiService } from '../../services/api';
import EnhancedDataDisplay from './EnhancedDataDisplay';
import { ProfileAPI } from '../../utils/unifiedProfileManager';
import { LogUtils, LOG_CATEGORIES, createLogger } from '../../utils/logger';
import dayjs from 'dayjs';

const { Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const logger = createLogger('ProfileDataModal');

const ProfileDataModal = ({ visible, onClose, profile }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [profileData, setProfileData] = useState(null);

  useEffect(() => {
    if (visible && profile) {
      loadProfileData();
    }
  }, [visible, profile]);

  const loadProfileData = async () => {
    if (!profile?.account_id) return;

    const userActionTimer = LogUtils.logUserAction('load_profile_data', { profileId: profile.id });
    setLoading(true);
    
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Loading profile data for account: ${profile.account_id}`, {
        profileId: profile.id,
        profileName: profile.name
      });
      
      const apiTimer = LogUtils.logApiCall('GET', '/api/getProfileData', { accountId: profile.account_id });
      const response = await apiService.getProfileData(profile.account_id);
      apiTimer.end();

      logger.debug(LOG_CATEGORIES.API, `Profile data response received for account: ${profile.account_id}`, {
        hasData: !!response.data,
        responseKeys: response.data ? Object.keys(response.data) : []
      });

      // Handle different response formats
      const responseData = response.data || response;
      
      if (responseData && responseData.success !== false) {
        // Check if response has success field
        if (responseData.success === true || responseData.success === undefined) {
          logger.info(LOG_CATEGORIES.PROFILE, `Profile data loaded successfully for account: ${profile.account_id}`);
          setProfileData(responseData);
        } else {
          // Handle error case
          const errorMsg = responseData.message || responseData.error || 'Unknown error';
          
          // Handle specific case where profile has no saved data
          if (errorMsg.includes('not found') || errorMsg.includes('No saved data')) {
            logger.info(LOG_CATEGORIES.PROFILE, `Profile has no saved data for account: ${profile.account_id}`);
            setProfileData(null);
            message.info('This profile has no saved browser data yet. Capture some data first.');
          } else {
            logger.error(LOG_CATEGORIES.PROFILE, `Failed to load profile data for account: ${profile.account_id}`, {
              error: errorMsg
            });
            message.error(`Failed to load profile data: ${errorMsg}`);
          }
        }
      } else {
        // If no success field, assume the response data is the profile data
        logger.debug(LOG_CATEGORIES.PROFILE, `No success field, treating response as profile data for account: ${profile.account_id}`);
        setProfileData(responseData);
      }
    } catch (error) {
      logger.error(LOG_CATEGORIES.PROFILE, `Load profile data error for account: ${profile.account_id}`, {
        error: error.message,
        stack: error.stack
      });
      
      // Enhanced error handling
      if (error.response?.status === 404) {
        logger.info(LOG_CATEGORIES.PROFILE, `Profile data not found (404) for account: ${profile.account_id}`);
        setProfileData(null);
        message.info('This profile has no saved browser data yet. Capture some data first.');
      } else {
        // Try to extract error message from response
        try {
          const errorData = error.response?.data;
          if (errorData && typeof errorData === 'object') {
            const errorMsg = errorData.message || errorData.detail || errorData.error || 'Unknown error';
            
            // Handle specific case where profile has no saved data
            if (errorMsg.includes('not found') || errorMsg.includes('No saved data')) {
              logger.info(LOG_CATEGORIES.PROFILE, `Profile has no saved data for account: ${profile.account_id}`);
              setProfileData(null);
              message.info('This profile has no saved browser data yet. Capture some data first.');
            } else {
              message.error(`Failed to load profile data: ${errorMsg}`);
            }
          } else {
            throw error; // Re-throw if we can't extract meaningful error info
          }
        } catch (retryError) {
          logger.error(LOG_CATEGORIES.PROFILE, `Retry error for account: ${profile.account_id}`, {
            error: retryError.message
          });
          const errorMsg = error.response?.data?.message || error.response?.data?.detail || error.message || 'Unknown error';
          message.error(`Failed to load profile data: ${errorMsg}`);
        }
      }
    } finally {
      setLoading(false);
      userActionTimer.end();
    }
  };

  const handleSaveProfile = async () => {
    if (!profile?.account_id) return;

    const userActionTimer = LogUtils.logUserAction('save_profile_data_modal', { profileId: profile.id });

    try {
      setSaving(true);

      logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Starting unified capture and save from modal for: ${profile.id}`, {
        profileName: profile.name,
        accountId: profile.account_id
      });

      // Use unified profile manager for complete flow
      const result = await ProfileAPI.captureAndSave(profile, {
        showLoadingMessage: true
      });

      if (result.success) {
        logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Unified capture and save completed in modal for profile: ${profile.id}`);

        // Reload the data to show updated information
        await loadProfileData();
      } else {
        logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Unified capture and save failed in modal for profile: ${profile.id}`, {
          error: result.message
        });
      }

    } catch (error) {
      logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Save profile operation failed in modal for: ${profile.id}`, {
        error: error.message,
        stack: error.stack
      });
      message.error('Failed to save profile data');
    } finally {
      setSaving(false);
      userActionTimer.end();
    }
  };

  if (!visible || !profile) return null;

  const downloadProfile = async () => {
    const userActionTimer = LogUtils.logUserAction('download_profile', { profileId: profile.id });
    
    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Starting profile download for: ${profile.id}`);
      
      const apiTimer = LogUtils.logApiCall('GET', `/api/profiles/${profile.id}/download`);
      const response = await apiService.get(`/api/profiles/${profile.id}/download`, {
        responseType: 'blob'
      });
      apiTimer.end();
      
      const blob = new Blob([response.data], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `profile_${profile.name}_${dayjs().format('YYYY-MM-DD')}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      logger.info(LOG_CATEGORIES.PROFILE, `Profile downloaded successfully for: ${profile.id}`);
      message.success('Profile downloaded successfully');
    } catch (error) {
      logger.error(LOG_CATEGORIES.PROFILE, `Profile download failed for: ${profile.id}`, {
        error: error.message
      });
      message.error('Failed to download profile');
    } finally {
      userActionTimer.end();
    }
  };

  return (
    <Modal
      title={
        <Space>
          <DatabaseOutlined />
          Profile Data - {profile?.name}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      style={{ top: 20 }}
      footer={[
        <Button 
          key="save" 
          type="primary" 
          icon={<SaveOutlined />} 
          loading={saving}
          onClick={handleSaveProfile}
        >
          Save Current Profile Data
        </Button>,
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
      ]}
    >
      <Spin spinning={loading}>
        {profileData ? (
          <Tabs defaultActiveKey="overview" type="card">
            <TabPane 
              tab={
                <Space>
                  <DatabaseOutlined />
                  Overview
                </Space>
              } 
              key="overview"
            >
              <Card>
                <Descriptions title="Profile Information" bordered column={2}>
                  <Descriptions.Item label="Profile Name">{profile.name}</Descriptions.Item>
                  <Descriptions.Item label="Account ID">{profile.account_id}</Descriptions.Item>
                  <Descriptions.Item label="Status">
                    <Tag color="green">{profile.status || 'Active'}</Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="Created">
                    {profile.created_at ? dayjs(profile.created_at).format('YYYY-MM-DD HH:mm:ss') : 'N/A'}
                  </Descriptions.Item>
                </Descriptions>

                <div style={{ marginTop: 16 }}>
                  <Space>
                    <Button
                      type="primary"
                      icon={<SaveOutlined />}
                      loading={saving}
                      onClick={handleSaveProfile}
                    >
                      Capture Current Browser Data
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={loadProfileData}
                    >
                      Reload Data
                    </Button>
                  </Space>
                </div>
              </Card>
            </TabPane>

            <TabPane 
              tab={
                <Space>
                  <SettingOutlined />
                  Browser Data
                </Space>
              } 
              key="data"
            >
              <EnhancedDataDisplay data={profileData} />
            </TabPane>
          </Tabs>
        ) : (
          <Empty
            description="No profile data available"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                loading={saving}
                onClick={handleSaveProfile}
              >
                Capture Browser Data
              </Button>
            </Space>
          </Empty>
        )}
      </Spin>
    </Modal>
  );
};

export default ProfileDataModal;
