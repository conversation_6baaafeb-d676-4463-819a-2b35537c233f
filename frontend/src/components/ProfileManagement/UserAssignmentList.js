/**
 * UserAssignmentList Component - Display and manage user profile group assignments
 */

import React, { useState } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Tag, 
  Popconfirm, 
  message, 
  Tooltip,
  Typography,
  Card,
  Badge
} from 'antd';
import { 
  EditOutlined, 
  DeleteOutlined, 
  UserAddOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { apiService } from '../../services/api';

const { Text } = Typography;

const UserAssignmentList = ({ 
  assignments = [], 
  loading = false, 
  onEdit, 
  onCreate, 
  onRefresh 
}) => {
  const [revoking, setRevoking] = useState(null);

  const handleRevoke = async (assignment) => {
    if (!assignment || !assignment.id) {
      message.error('Invalid assignment data');
      return;
    }

    setRevoking(assignment.id);
    try {
      await apiService.deleteUserProfileGroupAccess(assignment.id);
      message.success('User access revoked successfully!');
      onRefresh();
    } catch (error) {
      console.error('Revoke access error:', error);
      message.error(error.response?.data?.message || error.message || 'Revoke failed');
    } finally {
      setRevoking(null);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green';
      case 'suspended': return 'orange';
      case 'revoked': return 'red';
      default: return 'default';
    }
  };

  const getAccessLevelColor = (level) => {
    switch (level) {
      case 'read': return 'blue';
      case 'write': return 'orange';
      case 'full': return 'green';
      default: return 'default';
    }
  };

  const isExpired = (expiresAt) => {
    return expiresAt && dayjs(expiresAt).isBefore(dayjs());
  };

  const columns = [
    {
      title: 'User',
      dataIndex: ['user', 'email'],
      key: 'user',
      render: (email, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{email}</Text>
          {record.user?.name && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.user.name}
            </Text>
          )}
        </Space>
      ),
    },
    {
      title: 'Profile Group',
      dataIndex: ['profileGroup', 'name'],
      key: 'profileGroup',
      render: (text) => (
        <Tag color="blue">{text}</Tag>
      ),
    },
    {
      title: 'Access Level',
      dataIndex: ['permissions', 'access_level'],
      key: 'accessLevel',
      render: (level) => (
        <Tag color={getAccessLevelColor(level)}>
          {level?.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Permissions',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => (
        <Space direction="vertical" size={0}>
          {permissions?.can_launch_browser && (
            <Tag size="small" color="green">Launch Browser</Tag>
          )}
          {permissions?.can_view_profiles && (
            <Tag size="small" color="blue">View Profiles</Tag>
          )}
          {permissions?.can_export_data && (
            <Tag size="small" color="orange">Export Data</Tag>
          )}
        </Space>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => {
        const expired = isExpired(record.expires_at);
        if (expired && status === 'active') {
          return (
            <Space>
              <Tag color="red" icon={<ClockCircleOutlined />}>
                EXPIRED
              </Tag>
            </Space>
          );
        }
        return (
          <Tag color={getStatusColor(status)}>
            {status?.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: (date) => {
        if (!date) {
          return <Tag color="green">Never</Tag>;
        }
        const expired = isExpired(date);
        return (
          <Space direction="vertical" size={0}>
            <Text style={{ color: expired ? 'red' : 'inherit' }}>
              {dayjs(date).format('MMM DD, YYYY')}
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {dayjs(date).format('HH:mm')}
            </Text>
          </Space>
        );
      },
    },
    {
      title: 'Granted',
      dataIndex: 'granted_at',
      key: 'granted_at',
      render: (date) => (
        <Space direction="vertical" size={0}>
          <Text>{dayjs(date).format('MMM DD, YYYY')}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {dayjs(date).format('HH:mm')}
          </Text>
        </Space>
      ),
    },
    {
      title: 'Granted By',
      dataIndex: ['grantedByAdmin', 'email'],
      key: 'grantedBy',
      render: (email) => (
        <Text type="secondary">{email}</Text>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                // TODO: Implement view details modal
                message.info('View details feature coming soon');
              }}
            />
          </Tooltip>

          <Tooltip title="Edit Assignment">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => onEdit(record)}
              disabled={record.status === 'revoked'}
            />
          </Tooltip>

          <Popconfirm
            title="Revoke Access"
            description="Are you sure you want to revoke this user's access?"
            onConfirm={() => handleRevoke(record)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Revoke Access">
              <Button 
                type="text" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
                loading={revoking === record.id}
                disabled={record.status === 'revoked'}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="User Assignments"
      extra={
        <Button 
          type="primary" 
          icon={<UserAddOutlined />}
          onClick={onCreate}
        >
          Assign User
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={assignments.filter(assignment => assignment && typeof assignment === 'object')}
        loading={loading}
        rowKey={(record, index) => record?.id || `assignment-${index}`}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => 
            `${range[0]}-${range[1]} of ${total} assignments`,
        }}
        scroll={{ x: 1400 }}
      />
    </Card>
  );
};

export default UserAssignmentList;
