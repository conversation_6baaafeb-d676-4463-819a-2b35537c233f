/**
 * ProfileList Component - Display and manage profiles in a table
 * Refactored to use shared browser data capture utilities
 */

import React, { useState } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Popconfirm,
  message,
  Tooltip,
  Typography,
  Card
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  ChromeOutlined,
  SaveOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { apiService } from '../../services/api';
import ProfileDataModal from './ProfileDataModal';
import { ProfileAPI } from '../../utils/unifiedProfileManager';
import { LogUtils, LOG_CATEGORIES, createLogger } from '../../utils/logger';

const logger = createLogger('ProfileList');

const { Text } = Typography;

const ProfileList = ({
  profiles = [],
  loading = false,
  onEdit,
  onCreate,
  onRefresh,
  onDelete // New prop for custom delete function
}) => {
  const [deleting, setDeleting] = useState(null);
  const [profileDataModalVisible, setProfileDataModalVisible] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);

  const handleDelete = async (profile) => {
    const userActionTimer = LogUtils.logUserAction('delete_profile', { profileId: profile.id, profileName: profile.name });
    setDeleting(profile.id);

    try {
      logger.info(LOG_CATEGORIES.PROFILE, `Starting delete operation for profile: ${profile.id}`, {
        profileName: profile.name,
        accountId: profile.account_id
      });

      // Use custom delete function if provided, otherwise use default FastAPI endpoint
      if (onDelete) {
        await onDelete(profile.id);
      } else {
        const apiTimer = LogUtils.logApiCall('DELETE', `/api/profiles/${profile.id}`);
        const response = await apiService.delete(`/api/profiles/${profile.id}`);
        apiTimer.end();

        logger.debug(LOG_CATEGORIES.API, `Delete API response for profile: ${profile.id}`, {
          responseStatus: response.status,
          hasData: !!response.data
        });
      }

      logger.info(LOG_CATEGORIES.PROFILE, `Profile deleted successfully: ${profile.id}`, {
        profileName: profile.name
      });

      message.success(`Profile "${profile.name}" deleted successfully`);

      // Refresh the profiles list
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      logger.error(LOG_CATEGORIES.PROFILE, `Delete operation failed for profile: ${profile.id}`, {
        error: error.message,
        stack: error.stack,
        profileName: profile.name
      });

      const errorMessage = error.response?.data?.detail ||
                          error.response?.data?.message ||
                          error.message ||
                          'Failed to delete profile';
      message.error(errorMessage);
    } finally {
      setDeleting(null);
      userActionTimer.end();
    }
  };

  const handleLaunchBrowser = async (profile) => {
    const userActionTimer = LogUtils.logUserAction('launch_browser', { profileId: profile.id, profileName: profile.name });

    try {
      // First check if we have the account information
      if (!profile.account_id) {
        logger.error(LOG_CATEGORIES.VALIDATION, `Launch browser failed: No account associated with profile: ${profile.id}`, {
          profile
        });
        message.error('No account associated with this profile');
        return;
      }

      logger.info(LOG_CATEGORIES.BROWSER, `Launching browser for profile: ${profile.id}`, {
        profileName: profile.name,
        accountId: profile.account_id
      });

      // Launch browser using NestJS backend
      const apiTimer = LogUtils.logApiCall('POST', '/api/launchBrowserWithProfile', { accountId: profile.account_id });
      const response = await apiService.launchBrowserWithProfile(profile.account_id);
      apiTimer.end();

      logger.debug(LOG_CATEGORIES.API, `Launch browser API response for profile: ${profile.id}`, {
        responseStatus: response.status,
        hasData: !!response.data,
        dataType: typeof response.data,
        success: response.data?.success
      });

      if (response.data && response.data.success) {
        logger.info(LOG_CATEGORIES.BROWSER, `Browser launched successfully for profile: ${profile.id}`, {
          profileName: profile.name
        });
        message.success(`Browser launched successfully for profile: ${profile.name}`);
      } else {
        const errorMsg = response.data?.message || 'Unknown error occurred';
        logger.error(LOG_CATEGORIES.BROWSER, `Browser launch failed for profile: ${profile.id}`, {
          errorMessage: errorMsg,
          response: response.data
        });
        message.error(`Failed to launch browser: ${errorMsg}`);
      }
    } catch (error) {
      logger.error(LOG_CATEGORIES.BROWSER, `Launch browser error for profile: ${profile.id}`, {
        error: error.message,
        stack: error.stack,
        profileName: profile.name
      });
      message.error(error.response?.data?.message || error.message || 'Failed to launch browser');
    } finally {
      userActionTimer.end();
    }
  };

  const handleSaveProfile = async (profile) => {
    const userActionTimer = LogUtils.logUserAction('save_profile_data', { profileId: profile.id, profileName: profile.name });

    try {
      logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Starting unified capture and save for profile: ${profile.id}`, {
        profileName: profile.name,
        accountId: profile.account_id
      });

      // Use unified profile manager for complete flow
      const result = await ProfileAPI.captureAndSave(profile, {
        showLoadingMessage: true
      });

      if (result.success) {
        logger.info(LOG_CATEGORIES.DATA_CAPTURE, `Unified capture and save completed for profile: ${profile.id}`, {
          profileName: profile.name
        });

        // Refresh profile data if modal is open
        if (selectedProfile && selectedProfile.id === profile.id) {
          await loadProfileData();
        }

        // Trigger refresh callback if provided
        if (onRefresh) {
          onRefresh();
        }
      } else {
        logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Unified capture and save failed for profile: ${profile.id}`, {
          error: result.message,
          profileName: profile.name
        });
      }

    } catch (error) {
      logger.error(LOG_CATEGORIES.DATA_CAPTURE, `Save profile operation failed for: ${profile.id}`, {
        error: error.message,
        stack: error.stack,
        profileName: profile.name
      });
      message.error('Failed to save profile data');
    } finally {
      userActionTimer.end();
    }
  };

  const handleViewProfile = async (profile) => {
    const userActionTimer = LogUtils.logUserAction('view_profile', { profileId: profile.id, profileName: profile.name });

    try {
      // First check if we have the account information
      if (!profile.account_id) {
        logger.error(LOG_CATEGORIES.VALIDATION, `View profile failed: No account associated with profile: ${profile.id}`, {
          profile
        });
        message.error('No account associated with this profile');
        return;
      }

      logger.info(LOG_CATEGORIES.BROWSER, `Viewing profile: ${profile.id}`, {
        profileName: profile.name,
        accountId: profile.account_id
      });

      // Launch browser with saved profile data using NestJS backend
      const apiTimer = LogUtils.logApiCall('POST', '/api/viewProfile', { accountId: profile.account_id });
      const response = await apiService.viewProfile(profile.account_id);
      apiTimer.end();

      logger.debug(LOG_CATEGORIES.API, `View profile API response for profile: ${profile.id}`, {
        responseStatus: response.status,
        hasData: !!response.data,
        dataType: typeof response.data,
        success: response.data?.success
      });

      if (response.data && response.data.success) {
        logger.info(LOG_CATEGORIES.BROWSER, `Profile viewed successfully: ${profile.id}`, {
          profileName: profile.name
        });
        message.success(`Profile "${profile.name}" opened successfully with saved data`);
      } else {
        const errorMsg = response.data?.message || 'Unknown error occurred';
        logger.error(LOG_CATEGORIES.BROWSER, `View profile failed for profile: ${profile.id}`, {
          errorMessage: errorMsg,
          response: response.data
        });
        message.error(`Failed to view profile: ${errorMsg}`);
      }
    } catch (error) {
      logger.error(LOG_CATEGORIES.BROWSER, `View profile error for profile: ${profile.id}`, {
        error: error.message,
        stack: error.stack,
        profileName: profile.name
      });
      message.error(error.response?.data?.message || error.message || 'Failed to view profile');
    } finally {
      userActionTimer.end();
    }
  };

  const handleViewProfileData = (profile) => {
    setSelectedProfile(profile);
    setProfileDataModalVisible(true);
  };

  const loadProfileData = async () => {
    // This function is called when profile data modal needs to refresh
    // Implementation can be added if needed
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'orange';
      case 'archived': return 'red';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: 'Account ID',
      dataIndex: 'account_id',
      key: 'account_id',
      width: 100,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status?.toUpperCase() || 'UNKNOWN'}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date) => date ? dayjs(date).format('MM/DD HH:mm') : 'N/A',
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 350,
      render: (_, profile) => (
        <Space size="small">
          <Tooltip title="Edit Profile">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => onEdit && onEdit(profile)}
              size="small"
            />
          </Tooltip>

          <Tooltip title="View Profile (Open with saved data)">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewProfile(profile)}
              size="small"
              style={{ color: '#1890ff' }}
            />
          </Tooltip>

          <Tooltip title="Launch Browser">
            <Button
              type="text"
              icon={<ChromeOutlined />}
              onClick={() => handleLaunchBrowser(profile)}
              size="small"
            />
          </Tooltip>

          <Tooltip title="Save Browser Data">
            <Button
              type="text"
              icon={<SaveOutlined />}
              onClick={() => handleSaveProfile(profile)}
              size="small"
            />
          </Tooltip>

          <Tooltip title="View Profile Data">
            <Button
              type="text"
              icon={<DatabaseOutlined />}
              onClick={() => handleViewProfileData(profile)}
              size="small"
            />
          </Tooltip>

          <Popconfirm
            title="Delete Profile"
            description="Are you sure you want to delete this profile?"
            onConfirm={() => handleDelete(profile)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              loading={deleting === profile.id}
              danger
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title="Profiles"
      extra={
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={onCreate}
        >
          Create Profile
        </Button>
      }
    >
      <Table
        columns={columns}
        dataSource={profiles}
        loading={loading}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `Total ${total} profiles`,
        }}
        scroll={{ x: 800 }}
      />

      <ProfileDataModal
        visible={profileDataModalVisible}
        onClose={() => {
          setProfileDataModalVisible(false);
          setSelectedProfile(null);
        }}
        profile={selectedProfile}
      />
    </Card>
  );
};

export default ProfileList;
