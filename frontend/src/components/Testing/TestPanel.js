/**
 * Test Panel Component
 * Provides UI for running tests and viewing results
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Table,
  Tag,
  Progress,
  Alert,
  Collapse,
  Typography,
  Divider,
  Row,
  Col,
  Statistic,
  Modal,
  message
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  DownloadOutlined,
  DeleteOutlined,
  BugOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  ExperimentOutlined
} from '@ant-design/icons';
import { testRunner, TestUtils, MockData, TEST_SCENARIOS } from '../../utils/testUtils';
import { DebugUtils } from '../../utils/logger';
import { testFlowManager, ADMIN_CREDENTIALS } from '../../utils/testFlow';
import { runCompleteTestWithWorkingProfile } from '../../utils/profileHelper';
import { ProfileSyncUtils } from '../../utils/profileSync';

const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const TestPanel = ({ visible, onClose }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState([]);
  const [summary, setSummary] = useState(null);
  const [selectedTest, setSelectedTest] = useState(null);

  useEffect(() => {
    // Update results when tests complete
    const updateResults = () => {
      setResults([...testRunner.results]);
      setSummary(testRunner.getTestSummary());
    };

    // Check for updates periodically while tests are running
    let interval;
    if (isRunning) {
      interval = setInterval(updateResults, 500);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRunning]);

  const handleRunAllTests = async () => {
    setIsRunning(true);
    try {
      const testSummary = await testRunner.runAllTests({ showMessages: false });
      setSummary(testSummary);
      setResults([...testRunner.results]);
      
      if (testSummary.failedCount === 0) {
        message.success(`✅ All ${testSummary.totalCount} tests passed!`);
      } else {
        message.warning(`⚠️ ${testSummary.passedCount}/${testSummary.totalCount} tests passed`);
      }
    } catch (error) {
      message.error(`Test execution failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const handleRunSingleTest = async (scenario) => {
    setIsRunning(true);
    try {
      let testFunction;
      switch (scenario) {
        case TEST_SCENARIOS.VALIDATION_ERROR:
          testFunction = () => testRunner.testValidationError();
          break;
        case TEST_SCENARIOS.PROFILE_NOT_FOUND:
          testFunction = () => testRunner.testProfileNotFound();
          break;
        case TEST_SCENARIOS.PROFILE_SYNC_ISSUE:
          testFunction = () => testRunner.testProfileSyncIssue();
          break;
        default:
          throw new Error(`Unknown test scenario: ${scenario}`);
      }

      const result = await testRunner.runTest(scenario, testFunction, { showMessages: true });
      setResults([...testRunner.results]);
      setSummary(testRunner.getTestSummary());
    } catch (error) {
      message.error(`Test execution failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const handleClearResults = () => {
    testRunner.clearResults();
    setResults([]);
    setSummary(null);
    message.info('Test results cleared');
  };

  const handleExportResults = () => {
    testRunner.exportResults();
    message.success('Test results exported');
  };

  const handleExportLogs = () => {
    DebugUtils.exportLogs();
    message.success('Debug logs exported');
  };

  const handleRunAdminTestFlow = async () => {
    setIsRunning(true);
    try {
      await testFlowManager.runCompleteTestFlow();
      const flowResults = testFlowManager.getResults();

      // Convert flow results to test results format
      const convertedResults = flowResults.map(result => ({
        scenario: result.step.toLowerCase().replace(/\s+/g, '_'),
        success: result.status === 'success',
        message: result.message,
        duration: 0, // Flow results don't have duration
        timestamp: result.timestamp,
        data: result.data
      }));

      setResults([...testRunner.results, ...convertedResults]);
      setSummary(testRunner.getTestSummary());

    } catch (error) {
      message.error(`Admin test flow failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const handleRunWorkingProfileTest = async () => {
    setIsRunning(true);
    try {
      const result = await runCompleteTestWithWorkingProfile();

      // Add result to test results
      const testResult = {
        scenario: 'working_profile_test',
        success: result.summary.overallSuccess,
        message: result.summary.overallSuccess ? 'Working profile test completed successfully' : 'Working profile test completed with issues',
        duration: 0,
        timestamp: new Date().toISOString(),
        data: result
      };

      setResults([...results, testResult]);

      if (result.summary.overallSuccess) {
        message.success('✅ Working profile test completed successfully!');
      } else {
        message.warning('⚠️ Working profile test completed with some issues');
      }

    } catch (error) {
      message.error(`Working profile test failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const handleBulkSyncProfiles = async () => {
    setIsRunning(true);
    try {
      const result = await ProfileSyncUtils.bulkSync();

      // Add result to test results
      const testResult = {
        scenario: 'bulk_profile_sync',
        success: result.success,
        message: result.message,
        duration: 0,
        timestamp: new Date().toISOString(),
        data: result
      };

      setResults([...results, testResult]);

    } catch (error) {
      message.error(`Bulk sync failed: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success) => {
    return success ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    );
  };

  const getStatusTag = (success) => {
    return (
      <Tag color={success ? 'success' : 'error'}>
        {success ? 'PASSED' : 'FAILED'}
      </Tag>
    );
  };

  const columns = [
    {
      title: 'Status',
      dataIndex: 'success',
      key: 'status',
      width: 80,
      render: (success) => getStatusIcon(success),
    },
    {
      title: 'Scenario',
      dataIndex: 'scenario',
      key: 'scenario',
      render: (scenario) => (
        <Text code>{scenario.replace(/_/g, ' ').toUpperCase()}</Text>
      ),
    },
    {
      title: 'Result',
      dataIndex: 'success',
      key: 'result',
      width: 100,
      render: (success) => getStatusTag(success),
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: (duration) => `${duration.toFixed(2)}ms`,
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Button
          size="small"
          type="link"
          onClick={() => setSelectedTest(record)}
        >
          Details
        </Button>
      ),
    },
  ];

  const quickTestButtons = [
    {
      key: 'validation',
      label: 'Test Validation',
      scenario: TEST_SCENARIOS.VALIDATION_ERROR,
      icon: <BugOutlined />,
    },
    {
      key: 'not_found',
      label: 'Test Not Found',
      scenario: TEST_SCENARIOS.PROFILE_NOT_FOUND,
      icon: <CloseCircleOutlined />,
    },
    {
      key: 'sync_issue',
      label: 'Test Sync Issue',
      scenario: TEST_SCENARIOS.PROFILE_SYNC_ISSUE,
      icon: <ExperimentOutlined />,
    },
  ];

  return (
    <Modal
      title={
        <Space>
          <ExperimentOutlined />
          Testing & Validation Panel
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      style={{ top: 20 }}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
      ]}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Control Panel */}
        <Card title="Test Controls" size="small">
          <Space wrap>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              loading={isRunning}
              onClick={handleRunAllTests}
              disabled={isRunning}
            >
              Run All Tests
            </Button>

            <Button
              type="primary"
              icon={<ExperimentOutlined />}
              loading={isRunning}
              onClick={handleRunAdminTestFlow}
              disabled={isRunning}
              style={{ background: '#722ed1', borderColor: '#722ed1' }}
            >
              Run Admin Test Flow
            </Button>

            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              loading={isRunning}
              onClick={handleRunWorkingProfileTest}
              disabled={isRunning}
              style={{ background: '#52c41a', borderColor: '#52c41a' }}
            >
              Test Working Profile
            </Button>

            <Divider type="vertical" />
            
            {quickTestButtons.map(btn => (
              <Button
                key={btn.key}
                icon={btn.icon}
                loading={isRunning}
                onClick={() => handleRunSingleTest(btn.scenario)}
                disabled={isRunning}
                size="small"
              >
                {btn.label}
              </Button>
            ))}
            
            <Divider type="vertical" />
            
            <Button
              icon={<DeleteOutlined />}
              onClick={handleClearResults}
              disabled={isRunning}
            >
              Clear Results
            </Button>
            
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportResults}
              disabled={!results.length}
            >
              Export Results
            </Button>
            
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExportLogs}
            >
              Export Logs
            </Button>

            <Divider type="vertical" />

            <Button
              type="default"
              icon={<ExperimentOutlined />}
              loading={isRunning}
              onClick={handleBulkSyncProfiles}
              disabled={isRunning}
              style={{ background: '#1890ff', borderColor: '#1890ff', color: 'white' }}
            >
              Bulk Sync Profiles
            </Button>
          </Space>
        </Card>

        {/* Summary Statistics */}
        {summary && (
          <Card title="Test Summary" size="small">
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="Total Tests"
                  value={summary.totalCount}
                  prefix={<ExperimentOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Passed"
                  value={summary.passedCount}
                  valueStyle={{ color: '#3f8600' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Failed"
                  value={summary.failedCount}
                  valueStyle={{ color: '#cf1322' }}
                  prefix={<CloseCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="Duration"
                  value={summary.totalDuration.toFixed(2)}
                  suffix="ms"
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
            </Row>
            
            <div style={{ marginTop: 16 }}>
              <Progress
                percent={summary.passRate}
                status={summary.failedCount === 0 ? 'success' : 'exception'}
                format={(percent) => `${percent.toFixed(1)}% Pass Rate`}
              />
            </div>
          </Card>
        )}

        {/* Test Results */}
        <Card title="Test Results" size="small">
          {isRunning && (
            <Alert
              message="Tests are running..."
              description="Please wait while tests are being executed."
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}
          
          <Table
            columns={columns}
            dataSource={results}
            rowKey="timestamp"
            size="small"
            pagination={{ pageSize: 10 }}
            loading={isRunning}
          />
        </Card>

        {/* Mock Data Information */}
        <Collapse size="small">
          <Panel header="Mock Data & Test Information" key="mock-data">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="Test Information"
                description="These tests use mock data and scenarios to validate the browser data capture functionality. Use the Admin Test Flow to test with real admin credentials."
                type="info"
                showIcon
              />

              <Alert
                message="Admin Test Credentials"
                description={`Email: ${ADMIN_CREDENTIALS.email} | Password: ${ADMIN_CREDENTIALS.password}`}
                type="warning"
                showIcon
                style={{ marginTop: 8 }}
              />
              
              <Title level={5}>Available Mock Profiles:</Title>
              <ul>
                <li><Text code>validProfile</Text> - ID: {MockData.validProfile.id}, Status: Active</li>
                <li><Text code>invalidProfile</Text> - Missing required fields</li>
                <li><Text code>nonExistentProfile</Text> - ID: {MockData.nonExistentProfile.id}, Does not exist in backend</li>
              </ul>
              
              <Title level={5}>Test Scenarios:</Title>
              <ul>
                {Object.values(TEST_SCENARIOS).map(scenario => (
                  <li key={scenario}>
                    <Text code>{scenario.replace(/_/g, ' ')}</Text>
                  </li>
                ))}
              </ul>
            </Space>
          </Panel>
        </Collapse>
      </Space>

      {/* Test Detail Modal */}
      <Modal
        title="Test Details"
        open={!!selectedTest}
        onCancel={() => setSelectedTest(null)}
        footer={[
          <Button key="close" onClick={() => setSelectedTest(null)}>
            Close
          </Button>,
        ]}
      >
        {selectedTest && (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Paragraph>
              <Text strong>Scenario:</Text> {selectedTest.scenario}
            </Paragraph>
            <Paragraph>
              <Text strong>Status:</Text> {getStatusTag(selectedTest.success)}
            </Paragraph>
            <Paragraph>
              <Text strong>Duration:</Text> {selectedTest.duration.toFixed(2)}ms
            </Paragraph>
            <Paragraph>
              <Text strong>Timestamp:</Text> {new Date(selectedTest.timestamp).toLocaleString()}
            </Paragraph>
            <Paragraph>
              <Text strong>Message:</Text> {selectedTest.message}
            </Paragraph>
            {selectedTest.data && (
              <Paragraph>
                <Text strong>Data:</Text>
                <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                  {JSON.stringify(selectedTest.data, null, 2)}
                </pre>
              </Paragraph>
            )}
          </Space>
        )}
      </Modal>
    </Modal>
  );
};

export default TestPanel;
