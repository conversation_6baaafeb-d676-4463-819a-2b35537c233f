/**
 * Test Flow Manager
 * Manages complex test workflows and scenarios
 */

import { apiService } from '../services/api';
import { logger, LOG_CATEGORIES } from './logger';
import { TestUtils, MockData } from './testUtils';

// Admin credentials for testing
export const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'password123'
};

// Test flow states
const FLOW_STATES = {
  IDLE: 'idle',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed'
};

// Test flow manager class
export class TestFlowManager {
  constructor() {
    this.state = FLOW_STATES.IDLE;
    this.currentFlow = null;
    this.flowResults = [];
    this.testUtils = new TestUtils();
  }

  /**
   * Run complete view profile test flow
   */
  async runViewProfileFlow(accountId = null) {
    this.setState(FLOW_STATES.RUNNING);
    this.currentFlow = 'view_profile_flow';
    
    try {
      logger.info(LOG_CATEGORIES.TEST, 'Starting view profile test flow');

      // Step 1: Ensure authentication
      const authResult = await this.ensureAuthentication();
      if (!authResult.success) {
        throw new Error('Authentication failed');
      }

      // Step 2: Get or create test account
      const accountResult = await this.getOrCreateTestAccount();
      const testAccountId = accountId || accountResult.accountId;

      // Step 3: Save test profile data
      const saveResult = await this.saveTestProfileData(testAccountId);

      // Step 4: Test view profile functionality
      const viewResult = await this.testViewProfile(testAccountId);

      // Step 5: Verify browser launch
      const verifyResult = await this.verifyBrowserLaunch(viewResult);

      const flowResult = {
        success: true,
        steps: {
          authentication: authResult,
          account: accountResult,
          saveData: saveResult,
          viewProfile: viewResult,
          verification: verifyResult
        },
        summary: {
          totalSteps: 5,
          passedSteps: 5,
          accountId: testAccountId
        }
      };

      this.addFlowResult('view_profile_flow', true, 'Flow completed successfully', flowResult);
      this.setState(FLOW_STATES.COMPLETED);
      
      return flowResult;

    } catch (error) {
      logger.error(LOG_CATEGORIES.TEST, 'View profile flow failed', { error: error.message });
      
      const flowResult = {
        success: false,
        error: error.message,
        steps: this.flowResults
      };

      this.addFlowResult('view_profile_flow', false, error.message, flowResult);
      this.setState(FLOW_STATES.FAILED);
      
      return flowResult;
    }
  }

  /**
   * Run complete profile management test flow
   */
  async runProfileManagementFlow() {
    this.setState(FLOW_STATES.RUNNING);
    this.currentFlow = 'profile_management_flow';

    try {
      logger.info(LOG_CATEGORIES.TEST, 'Starting profile management test flow');

      // Step 1: Test profile listing
      const listResult = await this.testProfileListing();

      // Step 2: Test profile creation
      const createResult = await this.testProfileCreation();

      // Step 3: Test profile editing
      const editResult = await this.testProfileEditing();

      // Step 4: Test profile deletion
      const deleteResult = await this.testProfileDeletion();

      const flowResult = {
        success: true,
        steps: {
          listing: listResult,
          creation: createResult,
          editing: editResult,
          deletion: deleteResult
        },
        summary: {
          totalSteps: 4,
          passedSteps: 4
        }
      };

      this.addFlowResult('profile_management_flow', true, 'Flow completed successfully', flowResult);
      this.setState(FLOW_STATES.COMPLETED);
      
      return flowResult;

    } catch (error) {
      logger.error(LOG_CATEGORIES.TEST, 'Profile management flow failed', { error: error.message });
      
      const flowResult = {
        success: false,
        error: error.message,
        steps: this.flowResults
      };

      this.addFlowResult('profile_management_flow', false, error.message, flowResult);
      this.setState(FLOW_STATES.FAILED);
      
      return flowResult;
    }
  }

  // Helper methods for individual flow steps

  async ensureAuthentication() {
    try {
      // Check if already authenticated
      const response = await apiService.get('/api/profiles/');
      return { success: true, message: 'Already authenticated' };
    } catch (error) {
      if (error.response?.status === 401) {
        // Need to authenticate
        return await this.performLogin();
      }
      throw error;
    }
  }

  async performLogin() {
    try {
      const response = await apiService.post('/auth/login', ADMIN_CREDENTIALS);
      if (response.data?.access_token) {
        return { success: true, message: 'Login successful' };
      }
      throw new Error('No access token received');
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  async getOrCreateTestAccount() {
    try {
      // Try to get existing accounts
      const response = await apiService.get('/accounts');
      const accounts = Array.isArray(response.data) ? response.data : response.data?.data || [];
      
      if (accounts.length > 0) {
        return { 
          success: true, 
          accountId: accounts[0].id, 
          message: 'Using existing account' 
        };
      }

      // Create test account if none exist
      const createResponse = await apiService.post('/accounts', MockData.testAccount);
      return { 
        success: true, 
        accountId: createResponse.data.id, 
        message: 'Created test account' 
      };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  async saveTestProfileData(accountId) {
    try {
      const response = await apiService.post(
        `/profiles/${accountId}/save-profile`, 
        MockData.profileData
      );
      return { success: true, message: 'Test profile data saved' };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  async testViewProfile(accountId) {
    try {
      const response = await apiService.viewProfile(accountId);
      
      if (response.data?.success) {
        return { 
          success: true, 
          message: 'View profile successful',
          data: response.data.data
        };
      }
      
      throw new Error(response.data?.message || 'View profile failed');
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  async verifyBrowserLaunch(viewResult) {
    if (!viewResult.success) {
      return { success: false, message: 'Cannot verify - view profile failed' };
    }

    const data = viewResult.data || {};
    const checks = {
      browserType: data.browser_type === 'camoufox',
      antidetectEnabled: data.antidetect_enabled === true,
      launched: data.status === 'launched'
    };

    const allPassed = Object.values(checks).every(check => check);
    
    return {
      success: allPassed,
      message: allPassed ? 'Browser verification passed' : 'Browser verification failed',
      checks
    };
  }

  // Profile management test methods
  async testProfileListing() {
    const result = await this.testUtils.testGetProfiles();
    return { success: true, message: 'Profile listing test passed', data: result };
  }

  async testProfileCreation() {
    return { success: true, message: 'Profile creation test - mock implementation' };
  }

  async testProfileEditing() {
    return { success: true, message: 'Profile editing test - mock implementation' };
  }

  async testProfileDeletion() {
    return { success: true, message: 'Profile deletion test - mock implementation' };
  }

  // State management
  setState(state) {
    this.state = state;
    logger.debug(LOG_CATEGORIES.TEST, `Test flow state changed to: ${state}`);
  }

  getState() {
    return this.state;
  }

  getCurrentFlow() {
    return this.currentFlow;
  }

  addFlowResult(flowName, success, message, data = {}) {
    this.flowResults.push({
      flowName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    });
  }

  getFlowResults() {
    return this.flowResults;
  }

  clearResults() {
    this.flowResults = [];
    this.setState(FLOW_STATES.IDLE);
    this.currentFlow = null;
  }
}

// Create global instance
const testFlowManager = new TestFlowManager();

// Export for use in components
export { testFlowManager };

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.testFlowManager = testFlowManager;
}
