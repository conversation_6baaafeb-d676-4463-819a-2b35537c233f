/**
 * Interceptor Test Utilities
 * Provides testing functionality for API interceptors
 */

import { apiService } from '../services/api';
import { logger, LOG_CATEGORIES } from './logger';

class InterceptorTest {
  constructor() {
    this.testResults = [];
    this.isRunning = false;
  }

  /**
   * Run basic interceptor tests
   */
  async runBasicTests() {
    if (this.isRunning) {
      console.warn('Interceptor tests already running');
      return;
    }

    this.isRunning = true;
    this.testResults = [];

    try {
      logger.info(LOG_CATEGORIES.TEST, 'Starting interceptor tests');

      // Test 1: Basic API call
      await this.testBasicApiCall();

      // Test 2: Error handling
      await this.testErrorHandling();

      // Test 3: Authentication
      await this.testAuthentication();

      logger.info(LOG_CATEGORIES.TEST, 'Interceptor tests completed', {
        totalTests: this.testResults.length,
        passed: this.testResults.filter(r => r.passed).length,
        failed: this.testResults.filter(r => !r.passed).length
      });

    } catch (error) {
      logger.error(LOG_CATEGORIES.TEST, 'Interceptor test suite failed', {
        error: error.message
      });
    } finally {
      this.isRunning = false;
    }

    return this.testResults;
  }

  async testBasicApiCall() {
    try {
      const response = await apiService.get('/health');
      this.addTestResult('Basic API Call', true, 'Health check successful');
    } catch (error) {
      this.addTestResult('Basic API Call', false, error.message);
    }
  }

  async testErrorHandling() {
    try {
      await apiService.get('/non-existent-endpoint');
      this.addTestResult('Error Handling', false, 'Should have thrown error');
    } catch (error) {
      this.addTestResult('Error Handling', true, 'Error properly handled');
    }
  }

  async testAuthentication() {
    try {
      // This should work if user is logged in
      const response = await apiService.get('/api/profiles/');
      this.addTestResult('Authentication', true, 'Authenticated request successful');
    } catch (error) {
      if (error.response?.status === 401) {
        this.addTestResult('Authentication', true, 'Properly rejected unauthorized request');
      } else {
        this.addTestResult('Authentication', false, error.message);
      }
    }
  }

  addTestResult(testName, passed, message) {
    this.testResults.push({
      testName,
      passed,
      message,
      timestamp: new Date().toISOString()
    });
  }

  getResults() {
    return this.testResults;
  }
}

// Create global instance
const interceptorTest = new InterceptorTest();

// Auto-run tests in development
if (process.env.NODE_ENV === 'development') {
  // Run tests after a short delay to ensure everything is loaded
  setTimeout(() => {
    interceptorTest.runBasicTests().then(results => {
      console.log('🧪 Interceptor test results:', results);
    });
  }, 2000);
}

// Export for manual testing
window.interceptorTest = interceptorTest;

export default interceptorTest;
