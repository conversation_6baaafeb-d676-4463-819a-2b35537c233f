/**
 * Test Utils
 * Comprehensive testing utilities for the application
 */

import { apiService } from '../services/api';
import { logger, LOG_CATEGORIES } from './logger';

// Test scenarios configuration
export const TEST_SCENARIOS = {
  PROFILE_MANAGEMENT: 'profile_management',
  BROWSER_LAUNCH: 'browser_launch',
  VIEW_PROFILE: 'view_profile',
  DATA_CAPTURE: 'data_capture',
  AUTHENTICATION: 'authentication'
};

// Mock data for testing
export const MockData = {
  testProfile: {
    id: 999,
    name: 'Test Profile',
    account_id: 999,
    status: 'active',
    created_at: new Date().toISOString()
  },
  
  testAccount: {
    id: 999,
    website_url: 'https://facebook.com',
    username: 'test_user',
    name: 'Test Account',
    description: 'Test account for automated testing'
  },

  profileData: {
    localStorage: {
      test_key: 'test_value',
      user_preferences: 'test_preferences'
    },
    indexedDB: {
      test_db: { test_data: 'test_value' }
    },
    history: [
      { url: 'https://facebook.com', title: 'Facebook', timestamp: new Date().toISOString() }
    ],
    cookies: [
      { name: 'test_cookie', value: 'test_value', domain: 'facebook.com' }
    ]
  }
};

// Test utilities class
export class TestUtils {
  constructor() {
    this.testResults = [];
    this.isRunning = false;
  }

  /**
   * Run a test with error handling and logging
   */
  async runTest(testName, testFunction, ...args) {
    const startTime = Date.now();
    
    try {
      logger.info(LOG_CATEGORIES.TEST, `Starting test: ${testName}`);
      
      const result = await testFunction(...args);
      const duration = Date.now() - startTime;
      
      this.addTestResult(testName, true, 'Test passed', { duration, result });
      logger.info(LOG_CATEGORIES.TEST, `Test passed: ${testName}`, { duration });
      
      return { success: true, result, duration };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.addTestResult(testName, false, error.message, { duration, error: error.stack });
      logger.error(LOG_CATEGORIES.TEST, `Test failed: ${testName}`, { 
        error: error.message, 
        duration 
      });
      
      return { success: false, error: error.message, duration };
    }
  }

  /**
   * Test profile management functionality
   */
  async testProfileManagement() {
    const tests = [
      () => this.testGetProfiles(),
      () => this.testCreateProfile(),
      () => this.testUpdateProfile(),
      () => this.testDeleteProfile()
    ];

    const results = [];
    for (const test of tests) {
      const result = await this.runTest(`Profile Management - ${test.name}`, test);
      results.push(result);
    }

    return results;
  }

  /**
   * Test view profile functionality
   */
  async testViewProfile(accountId = 1) {
    return await this.runTest('View Profile', async () => {
      const response = await apiService.viewProfile(accountId);
      
      if (!response.data?.success) {
        throw new Error(`View profile failed: ${response.data?.message}`);
      }

      return {
        success: true,
        browserType: response.data.data?.browser_type,
        viewMode: response.data.data?.view_mode,
        profileDataLoaded: response.data.data?.profile_data_loaded
      };
    });
  }

  /**
   * Test browser launch functionality
   */
  async testBrowserLaunch(accountId = 1) {
    return await this.runTest('Browser Launch', async () => {
      const response = await apiService.launchBrowserWithProfile(accountId);
      
      if (!response.data?.success) {
        throw new Error(`Browser launch failed: ${response.data?.message}`);
      }

      return {
        success: true,
        browserType: response.data.data?.browser_type,
        antidetectEnabled: response.data.data?.antidetect_enabled
      };
    });
  }

  /**
   * Test authentication
   */
  async testAuthentication() {
    return await this.runTest('Authentication', async () => {
      const response = await apiService.get('/api/profiles/');
      
      if (!response.data) {
        throw new Error('Authentication failed - no data returned');
      }

      return {
        success: true,
        profileCount: Array.isArray(response.data) ? response.data.length : 0
      };
    });
  }

  // Helper methods for individual tests
  async testGetProfiles() {
    const response = await apiService.get('/api/profiles/');
    if (!Array.isArray(response.data)) {
      throw new Error('Expected array of profiles');
    }
    return response.data;
  }

  async testCreateProfile() {
    // Mock profile creation test
    return { message: 'Profile creation test - mock implementation' };
  }

  async testUpdateProfile() {
    // Mock profile update test
    return { message: 'Profile update test - mock implementation' };
  }

  async testDeleteProfile() {
    // Mock profile deletion test
    return { message: 'Profile deletion test - mock implementation' };
  }

  /**
   * Add test result to results array
   */
  addTestResult(testName, passed, message, metadata = {}) {
    this.testResults.push({
      testName,
      passed,
      message,
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }

  /**
   * Get all test results
   */
  getResults() {
    return this.testResults;
  }

  /**
   * Clear test results
   */
  clearResults() {
    this.testResults = [];
  }

  /**
   * Get test statistics
   */
  getStats() {
    const total = this.testResults.length;
    const passed = this.testResults.filter(r => r.passed).length;
    const failed = total - passed;
    const passRate = total > 0 ? (passed / total * 100).toFixed(2) : 0;

    return {
      total,
      passed,
      failed,
      passRate: `${passRate}%`
    };
  }
}

// Test runner class
export class TestRunner {
  constructor() {
    this.testUtils = new TestUtils();
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    logger.info(LOG_CATEGORIES.TEST, 'Starting comprehensive test suite');
    
    const results = {
      authentication: await this.testUtils.testAuthentication(),
      profileManagement: await this.testUtils.testProfileManagement(),
      viewProfile: await this.testUtils.testViewProfile(),
      browserLaunch: await this.testUtils.testBrowserLaunch()
    };

    const stats = this.testUtils.getStats();
    
    logger.info(LOG_CATEGORIES.TEST, 'Test suite completed', { stats, results });
    
    return {
      results,
      stats,
      details: this.testUtils.getResults()
    };
  }

  /**
   * Run specific test scenario
   */
  async runScenario(scenario, ...args) {
    switch (scenario) {
      case TEST_SCENARIOS.AUTHENTICATION:
        return await this.testUtils.testAuthentication();
      
      case TEST_SCENARIOS.PROFILE_MANAGEMENT:
        return await this.testUtils.testProfileManagement();
      
      case TEST_SCENARIOS.VIEW_PROFILE:
        return await this.testUtils.testViewProfile(...args);
      
      case TEST_SCENARIOS.BROWSER_LAUNCH:
        return await this.testUtils.testBrowserLaunch(...args);
      
      default:
        throw new Error(`Unknown test scenario: ${scenario}`);
    }
  }

  /**
   * Get test results
   */
  getResults() {
    return this.testUtils.getResults();
  }

  /**
   * Get test statistics
   */
  getStats() {
    return this.testUtils.getStats();
  }
}

// Create global instances
const globalTestRunner = new TestRunner();
const globalTestUtils = new TestUtils();

// Export for use in components
export { globalTestRunner as testRunner, globalTestUtils as testUtilsInstance };

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.testRunner = globalTestRunner;
  window.TestUtils = globalTestUtils;
  window.MockData = MockData;
}
