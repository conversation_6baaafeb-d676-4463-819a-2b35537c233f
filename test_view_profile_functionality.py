#!/usr/bin/env python3
"""
Test View Profile Functionality
This script tests the complete view profile workflow
"""

import asyncio
import aiohttp
import json
import sys
import os

# Test configuration
NESTJS_BASE_URL = "http://localhost:3000"
FASTAPI_BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3001"

# Admin credentials
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "password123"

async def test_login():
    """Test admin login"""
    print("🔐 Testing admin login...")
    
    async with aiohttp.ClientSession() as session:
        login_data = {
            "email": ADMIN_EMAIL,
            "password": ADMIN_PASSWORD
        }
        
        try:
            async with session.post(f"{NESTJS_BASE_URL}/auth/login", json=login_data) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    token = result.get('access_token')
                    if token:
                        print("✅ Admin login successful")
                        return token
                    else:
                        print("❌ No access token in response")
                        return None
                else:
                    error_text = await response.text()
                    print(f"❌ Login failed: {response.status} - {error_text}")
                    return None
        except Exception as e:
            print(f"❌ Login error: {e}")
            return None

async def test_get_accounts(token):
    """Test getting accounts list"""
    print("📋 Testing accounts retrieval...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    async with aiohttp.ClientSession() as session:
        try:
            # Try to get accounts from NestJS
            async with session.get(f"{NESTJS_BASE_URL}/accounts", headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    # Handle both direct array and wrapped response
                    accounts = result if isinstance(result, list) else result.get('data', [])
                    print(f"✅ Found {len(accounts)} accounts")
                    return accounts
                else:
                    print(f"⚠️ No accounts found in NestJS, status: {response.status}")
                    return []
        except Exception as e:
            print(f"❌ Error getting accounts: {e}")
            return []

async def test_create_test_account(token):
    """Create a test account for testing"""
    print("🆕 Creating test account...")
    
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    account_data = {
        "website_url": "https://facebook.com",
        "username": "test_view_profile_user",
        "password": "test_password",
        "name": "Test View Profile Account",
        "description": "Account created for testing view profile functionality"
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(f"{NESTJS_BASE_URL}/accounts", json=account_data, headers=headers) as response:
                if response.status == 201:
                    account = await response.json()
                    print(f"✅ Test account created with ID: {account.get('id')}")
                    return account
                else:
                    error_text = await response.text()
                    print(f"❌ Failed to create test account: {response.status} - {error_text}")
                    return None
        except Exception as e:
            print(f"❌ Error creating test account: {e}")
            return None

async def test_save_profile_data(token, account_id):
    """Test saving profile data"""
    print(f"💾 Testing profile data save for account {account_id}...")
    
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # Mock profile data
    profile_data = {
        "localStorage": {
            "user_preferences": "dark_mode",
            "session_id": "test_session_123"
        },
        "indexedDB": {
            "user_data": {"name": "Test User", "email": "<EMAIL>"}
        },
        "history": [
            {"url": "https://facebook.com", "title": "Facebook", "timestamp": "2025-07-28T22:00:00Z"},
            {"url": "https://facebook.com/profile", "title": "Profile", "timestamp": "2025-07-28T22:05:00Z"}
        ],
        "cookies": [
            {"name": "session_token", "value": "abc123", "domain": "facebook.com"},
            {"name": "user_id", "value": "12345", "domain": "facebook.com"}
        ]
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{NESTJS_BASE_URL}/profiles/{account_id}/save-profile", 
                json=profile_data, 
                headers=headers
            ) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    print("✅ Profile data saved successfully")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ Failed to save profile data: {response.status} - {error_text}")
                    return False
        except Exception as e:
            print(f"❌ Error saving profile data: {e}")
            return False

async def test_view_profile(token, account_id):
    """Test view profile functionality"""
    print(f"👁️ Testing view profile for account {account_id}...")
    
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # Request with viewMode flag
    request_data = {
        "viewMode": True
    }
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"{NESTJS_BASE_URL}/profiles/{account_id}/launch-browser", 
                json=request_data, 
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status in [200, 201]:
                    result = await response.json()
                    if result.get('success'):
                        print("✅ View profile successful!")
                        print(f"   Browser type: {result.get('data', {}).get('browser_type', 'unknown')}")
                        print(f"   Profile data loaded: {result.get('data', {}).get('profile_data_loaded', False)}")
                        print(f"   View mode: {result.get('data', {}).get('view_mode', False)}")
                        return True
                    else:
                        print(f"❌ View profile failed: {result.get('message', 'Unknown error')}")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ View profile request failed: {response.status} - {error_text}")
                    return False
        except Exception as e:
            print(f"❌ Error testing view profile: {e}")
            return False

async def main():
    """Main test function"""
    print("🚀 Testing View Profile Functionality")
    print("=" * 50)
    
    # Step 1: Login
    token = await test_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        return False
    
    # Step 2: Get existing accounts or create test account
    accounts = await test_get_accounts(token)
    
    if not accounts:
        # Create a test account
        test_account = await test_create_test_account(token)
        if not test_account:
            print("❌ Cannot proceed without test account")
            return False
        account_id = test_account.get('id')
    else:
        # Use first available account
        account_id = accounts[0].get('id')
        if not account_id:
            print("❌ No valid account ID found")
            return False
        print(f"📋 Using existing account ID: {account_id}")
    
    # Step 3: Save some test profile data
    if not await test_save_profile_data(token, account_id):
        print("⚠️ Profile data save failed, but continuing with view test...")
    
    # Step 4: Test view profile
    success = await test_view_profile(token, account_id)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 View Profile functionality test PASSED!")
        print("\nNext steps:")
        print("1. Open your browser and go to: http://localhost:3001")
        print("2. <NAME_EMAIL> / password123")
        print("3. Navigate to 'Manage Profiles' page")
        print("4. Look for the 'View' button (eye icon) in the Actions column")
        print("5. Click the 'View' button to test the UI integration")
    else:
        print("❌ View Profile functionality test FAILED!")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
